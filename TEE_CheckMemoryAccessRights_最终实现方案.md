# Trusty TEE_CheckMemoryAccessRights 最终实现方案

## 1. 方案概述

基于对OP-TEE实际实现的分析，采用**三层检查架构**：
1. **用户空间GP接口**：基础参数验证 + 系统调用
2. **系统调用MMU检查**：内核级别的内存权限验证
3. **用户空间扩展检查**：TA参数兼容性 + 堆内存安全

## 2. 实现步骤

### 步骤1：用户空间GP接口实现

#### 文件位置：`user/base/lib/libutee/tee_api.c`

```c
#include <tee_internal_api.h>
#include <rctee_syscalls.h>

/**
 * GP标准TEE_CheckMemoryAccessRights完整实现
 * 遵循OP-TEE的三步检查模式
 */
TEE_Result TEE_CheckMemoryAccessRights(uint32_t accessFlags, void *buffer, size_t size)
{
    uint32_t flags = accessFlags;

    /* GP标准：size为0时直接返回成功 */
    if (!size) 
        return TEE_SUCCESS;

    /* GP标准：NULL指针检查 */
    if (!buffer) 
        return TEE_ERROR_ACCESS_DENIED;

    /* GP标准：地址溢出检查 */
    if ((uintptr_t)buffer + size < (uintptr_t)buffer)
        return TEE_ERROR_ACCESS_DENIED;

    /* GP标准：标志位验证 */
    if (accessFlags & ~(TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE |
                       TEE_MEMORY_ACCESS_ANY_OWNER))
        return TEE_ERROR_BAD_PARAMETERS;

    /* 第一步：MMU权限检查 - 通过系统调用 */
    if (_rctee_check_memory_access_rights(buffer, size, accessFlags))
        return TEE_ERROR_ACCESS_DENIED;

    /* 第二步：TA参数兼容性检查 - 用户空间 */
    flags &= TEE_MEMORY_ACCESS_READ | TEE_MEMORY_ACCESS_WRITE | TEE_MEMORY_ACCESS_ANY_OWNER;
    if (check_mem_access_rights_params(flags, buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    /* 第三步：堆内存重叠检查 - 用户空间 */
    if (trusty_buffer_overlaps_heap(buffer, size) &&
        !trusty_buffer_is_within_allocated(buffer, size))
        return TEE_ERROR_ACCESS_DENIED;

    return TEE_SUCCESS;
}
```

### 步骤2：系统调用接口定义

#### 文件位置：`user/base/include/rctee_syscalls.h`

```c
/* 系统调用号定义 */
#define __NR_rctee_check_memory_access_rights  0x42

/* 系统调用声明 */
long _rctee_check_memory_access_rights(void *buffer, size_t size, uint32_t access_flags);
```

#### 文件位置：`kernel/rctee/lib/rctee/rctee_core/syscall_table.c`

```c
/* 系统调用表注册 */
DEF_SYSCALL(0x42, _rctee_check_memory_access_rights, long, 3, 
           user_addr_t buffer, uint32_t size, uint32_t access_flags)
```

### 步骤3：内核系统调用实现

#### 文件位置：`kernel/rctee/lib/rctee/rctee_core/syscall.c`

```c
#include <kernel/vm.h>
#include <arch/mmu.h>
#include <rctee_app.h>

/**
 * 内核系统调用实现 - 专注于MMU权限检查
 * 参考OP-TEE的vm_check_access_rights
 */
long sys__rctee_check_memory_access_rights(user_addr_t buffer, 
                                          uint32_t size, 
                                          uint32_t access_flags) {
    struct rctee_app *rctee_app = current_rctee_app();
    vaddr_t vaddr = (vaddr_t)buffer;
    vaddr_t end_addr;
    
    /* 基本参数验证 */
    if (!buffer || !size) 
        return ERR_INVALID_ARGS;
    
    if (add_overflow(vaddr, size, &end_addr))
        return ERR_INVALID_ARGS;
    
    /* 所有权检查 - 简化版 */
    if (!(access_flags & TEE_MEMORY_ACCESS_ANY_OWNER)) {
        if (!is_ta_private_memory(rctee_app, vaddr, size))
            return ERR_ACCESS_DENIED;
    }
    
    /* 逐页MMU权限检查 */
    while (vaddr < end_addr) {
        uint32_t mmu_flags;
        paddr_t paddr;
        
        status_t ret = arch_mmu_query(&rctee_app->aspace->arch_aspace, 
                                     vaddr, &paddr, &mmu_flags);
        if (ret != NO_ERROR)
            return ERR_ACCESS_DENIED;
        
        /* 检查基本用户权限 */
        if (!(mmu_flags & ARCH_MMU_FLAG_PERM_USER))
            return ERR_ACCESS_DENIED;
        
        /* 检查写权限 */
        if ((access_flags & TEE_MEMORY_ACCESS_WRITE)) {
            if (mmu_flags & ARCH_MMU_FLAG_PERM_RO)
                return ERR_ACCESS_DENIED;
        }
        
        vaddr = ROUNDUP(vaddr + 1, PAGE_SIZE);
    }
    
    return NO_ERROR;
}

/**
 * 简化的私有内存检查
 */
static bool is_ta_private_memory(struct rctee_app *rctee_app, 
                                vaddr_t vaddr, size_t size) {
    struct vmm_aspace *aspace = rctee_app->aspace;
    vaddr_t end_addr = vaddr + size;
    
    /* 检查是否在TA地址空间范围内 */
    if (vaddr < aspace->base || end_addr > (aspace->base + aspace->size)) {
        return false;
    }
    
    /* 简化实现：检查是否为共享内存 */
    struct vmm_region *region;
    list_for_every_entry(&aspace->region_list, region, struct vmm_region, node) {
        if (vaddr < (region->base + region->size) && end_addr > region->base) {
            /* 如果发现共享内存标志，则不是私有的 */
            if (region->flags & VMM_FLAG_SHARED) {
                return false;
            }
        }
    }
    
    return true;  // 默认为私有内存
}
```

### 步骤4：用户空间扩展检查实现

#### 文件位置：`user/base/lib/libutee/tee_api.c`

```c
/**
 * TA参数兼容性检查
 * 防止访问其他TA的参数内存
 */
static TEE_Result check_mem_access_rights_params(uint32_t flags, void *buffer, size_t size)
{
    /* 简化实现：检查是否与当前TA参数重叠 */
    struct ta_session *session = get_current_ta_session();
    if (!session) return TEE_SUCCESS;
    
    for (uint32_t i = 0; i < session->param_count; i++) {
        TEE_Param *param = &session->params[i];
        
        /* 检查内存引用参数 */
        if (param->memref.buffer && param->memref.size > 0) {
            /* 检查是否重叠 */
            if (buffers_overlap(buffer, size, 
                               param->memref.buffer, param->memref.size)) {
                /* 检查访问权限兼容性 */
                if (!check_access_compatibility(flags, param->memref.flags)) {
                    return TEE_ERROR_ACCESS_DENIED;
                }
            }
        }
    }
    
    return TEE_SUCCESS;
}

/**
 * 堆内存重叠检查
 */
static bool trusty_buffer_overlaps_heap(void *buffer, size_t size)
{
    /* 获取堆信息 */
    struct heap_info heap_info;
    if (!get_heap_info(&heap_info))
        return false;
    
    uintptr_t buf_start = (uintptr_t)buffer;
    uintptr_t buf_end = buf_start + size;
    uintptr_t heap_start = (uintptr_t)heap_info.base;
    uintptr_t heap_end = heap_start + heap_info.size;
    
    return (buf_start < heap_end && buf_end > heap_start);
}

/**
 * 检查缓冲区是否在已分配的堆内存内
 */
static bool trusty_buffer_is_within_allocated(void *buffer, size_t size)
{
    /* 使用分配器的usable_size函数检查 */
    size_t usable_size = malloc_usable_size(buffer);
    return (usable_size > 0 && size <= usable_size);
}
```

## 3. 实现优先级

### 第一阶段（最小可用版本）
1. **步骤1**：用户空间GP接口（去掉步骤2和3的调用）
2. **步骤2**：系统调用接口定义
3. **步骤3**：内核基础MMU检查

### 第二阶段（完整功能）
4. **步骤4**：添加用户空间扩展检查
5. 完善错误处理和边界情况

### 第三阶段（优化）
6. 性能优化
7. 完整测试覆盖

## 4. 关键设计决策

### 4.1 分层职责
- **用户空间**：GP标准检查 + 参数兼容性 + 堆安全
- **内核空间**：MMU权限查询 + 内存所有权验证

### 4.2 简化策略
- **去除过度复杂的内存分类**：专注于共享vs私有
- **简化权限映射**：USER权限包含读，RO控制写
- **渐进式实现**：先实现核心功能，再添加扩展

### 4.3 与OP-TEE对齐
- **三步检查模式**：MMU + 参数 + 堆
- **相同的检查逻辑**：逐页验证，语义检查
- **兼容的错误处理**：返回相同的GP错误码

## 5. 总结

这个方案的优势：
1. **结构清晰**：按照实际调用顺序组织
2. **实现简单**：去除了过度设计的复杂功能
3. **分阶段实施**：支持渐进式开发
4. **与OP-TEE一致**：遵循成熟的实现模式
